<?php
// 测试修复后的续费功能
require_once 'config.php';

echo "=== 续费功能测试 ===\n";

// 测试用户
$test_user = 'renewal_test_user';

// 清理测试数据
$conn->query("DELETE FROM user WHERE user = '{$test_user}'");
$conn->query("DELETE FROM orders WHERE user = '{$test_user}'");
$conn->query("DELETE FROM payment_log WHERE user = '{$test_user}'");

// 创建测试用户，设置当前会员到期时间为明天
$tomorrow = date('Y-m-d', strtotime('+1 day'));
$insertUser = $conn->prepare("INSERT INTO user (user, pass, vip, code) VALUES (?, 'test123', ?, 1)");
$insertUser->bind_param("ss", $test_user, $tomorrow);
$insertUser->execute();

echo "创建测试用户: {$test_user}\n";
echo "初始VIP到期: {$tomorrow}\n\n";

// 模拟续费流程
function simulateRenewal($conn, $user, $package, $description) {
    echo "=== {$description} ===\n";
    
    // 1. 获取续费前状态
    $userStmt = $conn->prepare("SELECT vip FROM user WHERE user = ?");
    $userStmt->bind_param("s", $user);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    $userInfo = $userResult->fetch_assoc();
    $beforeVip = $userInfo['vip'];
    
    echo "续费前VIP到期: {$beforeVip}\n";
    
    // 2. 创建测试订单
    $tradeNo = 'TEST_' . time() . rand(1000, 9999);
    $outTradeNo = 'OUT_' . time() . rand(1000, 9999);
    $price = ($package == '1') ? '9.90' : (($package == '3') ? '25.00' : '99.00');
    $expireTime = date('Y-m-d H:i:s', strtotime('+5 minutes'));
    
    $insertOrder = $conn->prepare("INSERT INTO orders (user, package, price, pay_type, trade_no, out_trade_no, status, expire_time) VALUES (?, ?, ?, 'alipay', ?, ?, 'pending', ?)");
    $insertOrder->bind_param("ssssss", $user, $package, $price, $tradeNo, $outTradeNo, $expireTime);
    $insertOrder->execute();
    
    echo "创建测试订单: {$tradeNo}\n";
    
    // 3. 模拟支付状态检查（只更新订单状态，不处理会员）
    $updateOrder = $conn->prepare("UPDATE orders SET status = 'paid', pay_time = NOW() WHERE trade_no = ?");
    $updateOrder->bind_param("s", $tradeNo);
    $updateOrder->execute();
    
    echo "订单状态已更新为已支付\n";
    
    // 4. 模拟 epay_notify.php 的续费逻辑
    // 检查是否已处理过
    $checkStmt = $conn->prepare("SELECT * FROM payment_log WHERE out_trade_no = ? AND status = 1");
    $checkStmt->bind_param("s", $outTradeNo);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    
    if ($checkResult->num_rows > 0) {
        echo "订单已处理过，跳过续费\n";
        return;
    }
    
    // 获取当前VIP状态
    $userStmt = $conn->prepare("SELECT vip FROM user WHERE user = ?");
    $userStmt->bind_param("s", $user);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    $userInfo = $userResult->fetch_assoc();
    $currentVip = $userInfo['vip'];
    
    // 计算新的VIP到期日期
    $currentDate = date('Y-m-d');
    $startDate = ($currentVip > $currentDate) ? $currentVip : $currentDate;
    
    echo "计算起始日期: {$startDate} (当前: {$currentDate}, 原到期: {$currentVip})\n";
    
    switch ($package) {
        case '1': // 月套餐
            $newVipDate = date('Y-m-d', strtotime($startDate . ' +1 month'));
            break;
        case '3': // 季套餐
            $newVipDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
            break;
        case 'year': // 年套餐
            $newVipDate = date('Y-m-d', strtotime($startDate . ' +1 year'));
            break;
        case 'forever': // 永久套餐
            $newVipDate = '2099-12-31';
            break;
        default:
            $newVipDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
    }
    
    // 更新用户VIP
    $updateUserStmt = $conn->prepare("UPDATE user SET vip = ? WHERE user = ?");
    $updateUserStmt->bind_param("ss", $newVipDate, $user);
    $updateUserStmt->execute();
    
    // 记录支付日志（防重复处理）
    $logStmt = $conn->prepare("INSERT INTO payment_log (user, package_type, price, vip_date, payment_time, out_trade_no, trade_no, status) VALUES (?, ?, ?, ?, NOW(), ?, ?, 1)");
    $logStmt->bind_param("ssssss", $user, $package, $price, $newVipDate, $outTradeNo, $tradeNo);
    $logStmt->execute();
    
    echo "续费完成！新VIP到期: {$newVipDate}\n";
    echo "预期增加时间: " . (($package == '1') ? '1个月' : (($package == '3') ? '3个月' : '1年')) . "\n\n";
    
    return $newVipDate;
}

// 测试续费
$result1 = simulateRenewal($conn, $test_user, '1', '月套餐续费测试');
$result2 = simulateRenewal($conn, $test_user, '3', '季套餐续费测试');

// 测试重复处理
echo "=== 重复处理测试 ===\n";
simulateRenewal($conn, $test_user, '1', '重复月套餐续费（应该被跳过）');

// 显示最终状态
$finalStmt = $conn->prepare("SELECT vip FROM user WHERE user = ?");
$finalStmt->bind_param("s", $test_user);
$finalStmt->execute();
$finalResult = $finalStmt->get_result();
$finalInfo = $finalResult->fetch_assoc();

echo "\n=== 测试结果 ===\n";
echo "初始VIP到期: {$tomorrow}\n";
echo "最终VIP到期: {$finalInfo['vip']}\n";

// 计算预期结果
$expected1 = date('Y-m-d', strtotime($tomorrow . ' +1 month'));
$expected2 = date('Y-m-d', strtotime($expected1 . ' +3 months'));

echo "预期VIP到期: {$expected2}\n";
echo "结果正确: " . (($finalInfo['vip'] == $expected2) ? '✅ 是' : '❌ 否') . "\n";

// 清理测试数据
$conn->query("DELETE FROM user WHERE user = '{$test_user}'");
$conn->query("DELETE FROM orders WHERE user = '{$test_user}'");
$conn->query("DELETE FROM payment_log WHERE user = '{$test_user}'");

echo "\n测试数据已清理\n";

$conn->close();
?>
