<?php
header('Content-Type: application/json; charset=utf-8');

// 关闭错误显示，防止HTML错误信息干扰JSON输出
error_reporting(0);
ini_set('display_errors', 0);

// 引入易支付配置
$config = include 'epay_config.php';

try {
    // 获取订单号
    $trade_no = $_GET['trade_no'] ?? '';
    $out_trade_no = $_GET['out_trade_no'] ?? '';

    if (empty($trade_no) && empty($out_trade_no)) {
        throw new Exception('缺少订单号参数');
    }

    // 构建查询URL
    $queryUrl = 'https://mpay.5xv.cn/xpay/epay/api.php?act=order&pid=' . $config['pid'] . '&key=' . $config['key'];

    if (!empty($trade_no)) {
        $queryUrl .= '&trade_no=' . urlencode($trade_no);
    } else {
        $queryUrl .= '&out_trade_no=' . urlencode($out_trade_no);
    }

    // 发送查询请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $queryUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        throw new Exception('查询请求失败: ' . $error);
    }

    if ($httpCode !== 200) {
        throw new Exception('查询请求HTTP错误: ' . $httpCode);
    }

    $result = json_decode($response, true);

    if (!$result) {
        throw new Exception('查询响应格式错误: ' . $response);
    }

    if ($result['code'] != 1) {
        throw new Exception($result['msg'] ?? '查询订单失败');
    }

    // 检查支付状态
    $orderInfo = $result;
    $isPaid = ($orderInfo['status'] == 1); // 1表示已支付

    $response = [
        'success' => true,
        'paid' => $isPaid,
        'trade_no' => $orderInfo['trade_no'] ?? '',
        'out_trade_no' => $orderInfo['out_trade_no'] ?? '',
        'money' => $orderInfo['money'] ?? '',
        'type' => $orderInfo['type'] ?? '',
        'name' => $orderInfo['name'] ?? '',
        'addtime' => $orderInfo['addtime'] ?? '',
        'endtime' => $orderInfo['endtime'] ?? '',
        'status' => $orderInfo['status'] ?? 0,
        'message' => $isPaid ? '支付成功' : '等待支付'
    ];

    // 如果支付成功，更新用户会员状态
    if ($isPaid) {
        try {
            // 连接数据库
            $servername = "localhost";
            $username = "fbsbs";
            $password = "fbsbsxcx...";
            $dbname = "fbsbs";

            $conn = new mysqli($servername, $username, $password, $dbname);

            if ($conn->connect_error) {
                error_log("数据库连接失败: " . $conn->connect_error);
                // 即使数据库连接失败，也返回支付成功状态
                $response['db_error'] = '数据库连接失败，但支付已成功';
            } else {
                $conn->set_charset("utf8");

                // 查询本地订单信息
                $stmt = $conn->prepare("SELECT * FROM orders WHERE trade_no = ? OR out_trade_no = ?");
                $stmt->bind_param("ss", $orderInfo['trade_no'], $orderInfo['out_trade_no']);
                $stmt->execute();
                $localOrderResult = $stmt->get_result();

                if ($localOrderResult->num_rows > 0) {
                    $localOrder = $localOrderResult->fetch_assoc();
                    $user = $localOrder['user'];
                    $package = $localOrder['package'];

                    // 更新本地订单状态为已支付
                    $updateOrderStmt = $conn->prepare("UPDATE orders SET status = 'paid', pay_time = NOW() WHERE id = ?");
                    $updateOrderStmt->bind_param("i", $localOrder['id']);
                    $updateOrderStmt->execute();

                    // ⚠️ 重要：不在这里处理会员续费逻辑
                    // 会员续费由 epay_notify.php 统一处理，避免重复处理
                    $response['order_updated'] = true;
                    $response['package'] = $package;
                    $response['user'] = $user;
                    $response['note'] = '订单状态已更新，会员续费将由回调处理';

                    error_log("订单状态已更新: 用户={$user}, 套餐={$package}, 订单号={$localOrder['trade_no']}");
                }

                $conn->close();
            }
        } catch (Exception $e) {
            error_log("更新用户会员状态失败: " . $e->getMessage());
        }

        // 记录支付成功日志
        $logData = [
            'time' => date('Y-m-d H:i:s'),
            'action' => 'payment_success_check',
            'trade_no' => $orderInfo['trade_no'],
            'out_trade_no' => $orderInfo['out_trade_no'],
            'money' => $orderInfo['money'],
            'type' => $orderInfo['type']
        ];
        error_log("支付成功检查: " . json_encode($logData));
    }

    echo json_encode($response);

} catch (Exception $e) {
    $errorResponse = [
        'success' => false,
        'paid' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'trade_no' => $trade_no ?? '',
            'out_trade_no' => $out_trade_no ?? '',
            'query_url' => $queryUrl ?? ''
        ]
    ];

    echo json_encode($errorResponse);
}
?>
