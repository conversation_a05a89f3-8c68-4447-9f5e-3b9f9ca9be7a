<?php
// 引入qrlib文件
require_once 'phpqrcode/qrlib.php';

// 设置响应头
header('Content-Type: image/png');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// 获取二维码内容参数
$qrcode_content = $_GET['content'] ?? '';

// URL解码（防止特殊字符问题）
$qrcode_content = urldecode($qrcode_content);

// 生成二维码并输出到浏览器
// 参数说明：内容, 输出文件(false表示直接输出), 纠错级别, 大小, 边距
QRcode::png($qrcode_content, false, QR_ECLEVEL_M, 8, 2);
?>