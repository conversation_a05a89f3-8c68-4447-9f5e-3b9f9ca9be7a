<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支付模态框设计预览</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .test-button:hover {
            background: #005a87;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            max-width: 400px;
            width: 100%;
            position: relative;
        }
        
        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .close-btn:hover {
            color: #333;
        }
        
        .top-left-info {
            position: absolute;
            top: 15px;
            left: 20px;
            text-align: left;
            font-size: 12px;
            color: #333;
            font-weight: bold;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            background: #f0f0f0;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ddd;
            font-size: 14px;
            color: #666;
        }
        
        .amount-warning {
            margin: 10px 0;
            padding: 10px;
            background: #fff5f5;
            border: 1px solid #ff4757;
            border-radius: 6px;
            text-align: center;
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>支付模态框设计预览</h1>
        
        <div class="test-info">
            <h3>设计改进：</h3>
            <ul>
                <li>✅ 扫码支付和订单号显示在左上角</li>
                <li>✅ 支付金额字体加大，更醒目</li>
                <li>✅ 二维码下方添加红色金额警告</li>
                <li>✅ 根据支付方式显示对应提示文字</li>
                <li>✅ 只能通过X按钮关闭</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="openTestModal('wxpay')">微信支付预览</button>
        <button class="test-button" onclick="openTestModal('alipay')">支付宝支付预览</button>
        
        <div id="testModal" class="modal">
            <div class="modal-content">
                <button class="close-btn" onclick="closeTestModal()">×</button>
                
                <div class="top-left-info">
                    扫码支付 | 订单号: 202508300026075007001
                </div>

                <div style="margin: 30px 0 10px 0; color: #333; font-size: 18px; font-weight: bold;">
                    支付金额: <strong style="font-size: 24px; color: #ff6b6b;">¥0.01</strong>
                </div>

                <div style="margin-bottom: 10px; color: #ff6b6b; font-size: 14px; font-weight: bold; text-align: center;">
                    ⏰ 订单有效期: 01:40
                </div>

                <div style="margin: 10px 0; display: flex; justify-content: center; min-height: 220px; align-items: center;">
                    <div class="qr-code">
                        模拟二维码<br>
                        (测试用)
                    </div>
                </div>

                <div class="amount-warning">
                    <div style="color: #ff4757; font-weight: bold; font-size: 16px; margin-bottom: 5px;">
                        ⚠️ 请确认支付金额：¥0.01
                    </div>
                    <div style="color: #ff4757; font-size: 14px; line-height: 1.4;">
                        截图扫码支付 ¥0.01，<br>不要多付也不要少付！
                    </div>
                </div>

                <div id="paymentTip" style="margin: 10px 0; color: #666; font-size: 14px;">
                    请使用手机扫码完成支付
                </div>
            </div>
        </div>
    </div>

    <script>
        function openTestModal(payType) {
            const modal = document.getElementById('testModal');
            const paymentTip = document.getElementById('paymentTip');
            
            // 根据支付方式设置提示文字
            if (payType === 'wxpay') {
                paymentTip.innerHTML = '请使用<span style="color: #ff4757; font-weight: bold;">微信</span>扫码完成支付';
            } else if (payType === 'alipay') {
                paymentTip.innerHTML = '请使用<span style="color: #ff4757; font-weight: bold;">支付宝</span>扫码完成支付';
            } else {
                paymentTip.innerHTML = '请使用手机扫码完成支付';
            }
            
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            console.log('模态框已打开，支付方式:', payType);
        }
        
        function closeTestModal() {
            const modal = document.getElementById('testModal');
            modal.style.display = 'none';
            document.body.style.overflow = '';
            
            console.log('模态框已关闭');
        }
        
        // 阻止ESC键关闭
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('testModal');
            
            if (e.key === 'Escape' && modal.style.display === 'flex') {
                e.preventDefault();
                e.stopPropagation();
                
                // 添加震动效果
                const modalContent = modal.querySelector('.modal-content');
                modalContent.style.animation = 'shake 0.3s ease-in-out';
                
                setTimeout(function() {
                    modalContent.style.animation = '';
                }, 300);
            }
        });
        
        // 添加震动动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes shake {
                0%, 100% { transform: translateX(0); }
                25% { transform: translateX(-5px); }
                75% { transform: translateX(5px); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>
