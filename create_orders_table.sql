-- 创建订单表 (orders)
-- 用于记录用户的支付订单信息和有效期管理

CREATE TABLE IF NOT EXISTS `orders` (
    `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    `user` varchar(50) NOT NULL COMMENT '用户名',
    `package` varchar(20) NOT NULL COMMENT '套餐类型 (1=月会员, 3=季会员, year=年会员, forever=永久会员)',
    `price` decimal(10,2) NOT NULL COMMENT '订单金额',
    `pay_type` varchar(20) NOT NULL COMMENT '支付方式 (alipay=支付宝, wxpay=微信)',
    `trade_no` varchar(100) DEFAULT NULL COMMENT '易支付平台订单号',
    `out_trade_no` varchar(100) NOT NULL COMMENT '商户订单号',
    `status` varchar(20) NOT NULL DEFAULT 'pending' COMMENT '订单状态 (pending=待支付, paid=已支付, expired=已过期, cancelled=已取消)',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '订单创建时间',
    `expire_time` datetime DEFAULT NULL COMMENT '订单过期时间 (创建后5分钟)',
    `pay_time` datetime DEFAULT NULL COMMENT '支付完成时间',
    `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_trade_no` (`trade_no`) COMMENT '易支付订单号唯一索引',
    UNIQUE KEY `uk_out_trade_no` (`out_trade_no`) COMMENT '商户订单号唯一索引',
    KEY `idx_user` (`user`) COMMENT '用户索引',
    KEY `idx_status` (`status`) COMMENT '订单状态索引',
    KEY `idx_expire_time` (`expire_time`) COMMENT '过期时间索引',
    KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单表 - 记录用户支付订单信息';

-- 插入示例数据 (可选)
-- INSERT INTO `orders` (`user`, `package`, `price`, `pay_type`, `out_trade_no`, `status`, `expire_time`) 
-- VALUES 
-- ('testuser', '1', 9.90, 'alipay', '20250829150001', 'pending', DATE_ADD(NOW(), INTERVAL 5 MINUTE)),
-- ('testuser2', '3', 25.00, 'wxpay', '20250829150002', 'paid', DATE_ADD(NOW(), INTERVAL 5 MINUTE));

-- 查看表结构
-- DESCRIBE orders;

-- 查看表数据
-- SELECT * FROM orders ORDER BY create_time DESC LIMIT 10;
