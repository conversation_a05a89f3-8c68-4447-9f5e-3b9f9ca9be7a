<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框关闭测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .test-button:hover {
            background: #005a87;
        }
        
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.8);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            max-width: 400px;
            width: 90%;
            position: relative;
            text-align: center;
        }
        
        .close-btn {
            position: absolute;
            top: 10px;
            right: 15px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        
        .close-btn:hover {
            color: #333;
        }
        
        .qr-code {
            width: 200px;
            height: 200px;
            background: #f0f0f0;
            margin: 20px auto;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #ddd;
            font-size: 14px;
            color: #666;
        }
        
        .shake {
            animation: shake 0.3s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #2196f3;
        }
        
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .test-info ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>支付模态框关闭功能测试</h1>
        
        <div class="test-info">
            <h3>测试说明：</h3>
            <ul>
                <li>点击下方按钮打开模态框</li>
                <li>尝试点击模态框外的背景区域 - 应该<strong>不会关闭</strong></li>
                <li>尝试按 ESC 键 - 应该<strong>不会关闭</strong>，并有震动效果</li>
                <li>只有点击右上角的 ✕ 按钮才能关闭模态框</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="openTestModal()">打开支付二维码模态框</button>
        
        <div id="testModal" class="modal">
            <div class="modal-content">
                <button class="close-btn" onclick="closeTestModal()">✕</button>
                <h3>扫码支付</h3>
                <p>支付金额: ¥0.02</p>
                <p style="color: #ff6b6b;">⏰ 订单有效期: 04:48</p>
                <div class="qr-code">
                    模拟二维码<br>
                    (测试用)
                </div>
                <p>订单号: 202508300026075007001</p>
                <p>请使用手机扫码完成支付</p>
            </div>
        </div>
    </div>

    <script>
        function openTestModal() {
            const modal = document.getElementById('testModal');
            modal.style.display = 'flex';
            document.body.style.overflow = 'hidden';
            
            console.log('模态框已打开');
            console.log('测试：尝试点击背景或按ESC键，应该不会关闭');
        }
        
        function closeTestModal() {
            const modal = document.getElementById('testModal');
            modal.style.display = 'none';
            document.body.style.overflow = '';
            
            console.log('模态框已通过X按钮关闭');
        }
        
        // 阻止点击背景关闭（已移除此功能）
        // 不添加背景点击事件监听器
        
        // 阻止ESC键关闭，并添加震动效果
        document.addEventListener('keydown', function(e) {
            const modal = document.getElementById('testModal');
            
            if (e.key === 'Escape' && modal.style.display === 'flex') {
                e.preventDefault();
                e.stopPropagation();
                
                console.log('ESC键被阻止，添加震动效果');
                
                // 添加震动效果
                const modalContent = modal.querySelector('.modal-content');
                modalContent.classList.add('shake');
                
                setTimeout(function() {
                    modalContent.classList.remove('shake');
                }, 300);
            }
        });
        
        // 阻止模态框内容区域的点击事件冒泡
        document.querySelector('.modal-content').addEventListener('click', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>
