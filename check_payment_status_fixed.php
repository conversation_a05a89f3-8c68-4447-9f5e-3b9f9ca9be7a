<?php
header('Content-Type: application/json; charset=utf-8');

// 关闭错误显示，防止HTML错误信息干扰JSON输出
error_reporting(0);
ini_set('display_errors', 0);

try {
    // 获取订单号
    $trade_no = $_GET['trade_no'] ?? '';
    $out_trade_no = $_GET['out_trade_no'] ?? '';

    if (empty($trade_no) && empty($out_trade_no)) {
        echo json_encode(['success' => false, 'paid' => false, 'message' => '缺少订单号参数']);
        exit;
    }

    // 引入易支付配置
    $config = include 'epay_config.php';
    
    if (!$config) {
        echo json_encode(['success' => false, 'paid' => false, 'message' => '配置文件加载失败']);
        exit;
    }

    // 构建查询URL
    $queryUrl = 'https://mpay.5xv.cn/xpay/epay/api.php?act=order&pid=' . $config['pid'] . '&key=' . $config['key'];

    if (!empty($trade_no)) {
        $queryUrl .= '&trade_no=' . urlencode($trade_no);
    } else {
        $queryUrl .= '&out_trade_no=' . urlencode($out_trade_no);
    }

    // 发送查询请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $queryUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);

    if ($error) {
        echo json_encode(['success' => false, 'paid' => false, 'message' => '查询请求失败: ' . $error]);
        exit;
    }

    if ($httpCode !== 200) {
        echo json_encode(['success' => false, 'paid' => false, 'message' => '查询请求HTTP错误: ' . $httpCode]);
        exit;
    }

    $result = json_decode($response, true);

    if (!$result) {
        echo json_encode(['success' => false, 'paid' => false, 'message' => '查询响应格式错误']);
        exit;
    }

    if ($result['code'] != 1) {
        echo json_encode(['success' => false, 'paid' => false, 'message' => $result['msg'] ?? '查询订单失败']);
        exit;
    }

    // 检查支付状态
    $orderInfo = $result;
    $isPaid = ($orderInfo['status'] == 1); // 1表示已支付

    // 先检查本地订单是否过期
    $isExpired = false;
    $expireTime = null;

    try {
        // 连接数据库检查本地订单
        $servername = "localhost";
        $username = "fbsbs";
        $password = "fbsbsxcx...";
        $dbname = "fbsbs";

        $conn = new mysqli($servername, $username, $password, $dbname);

        if (!$conn->connect_error) {
            $conn->set_charset("utf8");

            // 查询本地订单
            $stmt = $conn->prepare("SELECT expire_time FROM orders WHERE trade_no = ? OR out_trade_no = ?");
            if ($stmt) {
                $stmt->bind_param("ss", $orderInfo['trade_no'], $orderInfo['out_trade_no']);
                $stmt->execute();
                $localResult = $stmt->get_result();

                if ($localResult->num_rows > 0) {
                    $localOrder = $localResult->fetch_assoc();
                    $expireTime = $localOrder['expire_time'];

                    if ($expireTime && strtotime($expireTime) < time()) {
                        $isExpired = true;
                    }
                }
            }
            $conn->close();
        }
    } catch (Exception $e) {
        // 数据库错误不影响支付状态检查
    }

    $finalResponse = [
        'success' => true,
        'paid' => $isPaid,
        'expired' => $isExpired,
        'expire_time' => $expireTime,
        'trade_no' => $orderInfo['trade_no'] ?? '',
        'out_trade_no' => $orderInfo['out_trade_no'] ?? '',
        'money' => $orderInfo['money'] ?? '',
        'type' => $orderInfo['type'] ?? '',
        'name' => $orderInfo['name'] ?? '',
        'addtime' => $orderInfo['addtime'] ?? '',
        'endtime' => $orderInfo['endtime'] ?? '',
        'status' => $orderInfo['status'] ?? 0,
        'message' => $isExpired ? '订单已过期' : ($isPaid ? '支付成功' : '等待支付')
    ];

    // 如果支付成功，尝试更新用户会员状态
    if ($isPaid) {
        try {
            // 连接数据库
            $servername = "localhost";
            $username = "fbsbs";
            $password = "fbsbsxcx...";
            $dbname = "fbsbs";
            
            $conn = new mysqli($servername, $username, $password, $dbname);

            if (!$conn->connect_error) {
                $conn->set_charset("utf8");

                // 查询本地订单信息
                $stmt = $conn->prepare("SELECT * FROM orders WHERE trade_no = ? OR out_trade_no = ?");
                if ($stmt) {
                    $stmt->bind_param("ss", $orderInfo['trade_no'], $orderInfo['out_trade_no']);
                    $stmt->execute();
                    $localOrderResult = $stmt->get_result();

                    if ($localOrderResult->num_rows > 0) {
                        $localOrder = $localOrderResult->fetch_assoc();
                        $user = $localOrder['user'];
                        $package = $localOrder['package'];

                        // 检查订单是否过期
                        $expire_time = $localOrder['expire_time'] ?? null;
                        $is_expired = false;
                        if ($expire_time && strtotime($expire_time) < time()) {
                            $is_expired = true;
                            $finalResponse['order_expired'] = true;
                            $finalResponse['expire_time'] = $expire_time;
                        }

                        // 检查订单是否已经处理过
                        if ($localOrder['status'] !== 'paid' && !$is_expired) {
                            // 更新本地订单状态为已支付
                            $updateOrderStmt = $conn->prepare("UPDATE orders SET status = 'paid', pay_time = NOW() WHERE id = ?");
                            if ($updateOrderStmt) {
                                $updateOrderStmt->bind_param("i", $localOrder['id']);
                                $updateOrderStmt->execute();
                            }

                            // ⚠️ 重要：不在这里处理会员续费逻辑
                            // 会员续费由 epay_notify.php 统一处理，避免重复处理
                            $finalResponse['order_updated'] = true;
                            $finalResponse['package'] = $package;
                            $finalResponse['user'] = $user;
                            $finalResponse['note'] = '订单状态已更新，会员续费将由回调处理';

                            error_log("订单状态已更新: 用户={$user}, 套餐={$package}, 订单号={$localOrder['trade_no']}");
                        } else {
                            $finalResponse['already_processed'] = true;
                            if ($is_expired) {
                                $finalResponse['expired_reason'] = '订单已过期，无法处理';
                            }
                        }
                    }
                }

                $conn->close();
            }
        } catch (Exception $e) {
            // 数据库操作失败不影响支付状态返回
            $finalResponse['db_error'] = $e->getMessage();
        }
    }

    echo json_encode($finalResponse);

} catch (Exception $e) {
    $errorResponse = [
        'success' => false,
        'paid' => false,
        'message' => $e->getMessage()
    ];

    echo json_encode($errorResponse);
}
?>
