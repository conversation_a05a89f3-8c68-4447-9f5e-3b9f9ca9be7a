<?php
// 测试续费功能
require_once 'config.php';

// 测试用户
$test_user = 'testuser';

// 创建测试用户（如果不存在）
$checkUser = $conn->prepare("SELECT * FROM user WHERE user = ?");
$checkUser->bind_param("s", $test_user);
$checkUser->execute();
$result = $checkUser->get_result();

if ($result->num_rows == 0) {
    // 创建测试用户，设置当前会员到期时间为明天
    $tomorrow = date('Y-m-d', strtotime('+1 day'));
    $insertUser = $conn->prepare("INSERT INTO user (user, pass, vip, code) VALUES (?, 'test123', ?, 1)");
    $insertUser->bind_param("ss", $test_user, $tomorrow);
    $insertUser->execute();
    echo "创建测试用户: {$test_user}, 当前VIP到期: {$tomorrow}\n";
} else {
    $user_info = $result->fetch_assoc();
    echo "测试用户已存在: {$test_user}, 当前VIP到期: {$user_info['vip']}\n";
}

// 测试续费逻辑
function testRenewal($conn, $user, $package, $description) {
    echo "\n=== 测试 {$description} ===\n";
    
    // 获取当前VIP状态
    $userStmt = $conn->prepare("SELECT vip FROM user WHERE user = ?");
    $userStmt->bind_param("s", $user);
    $userStmt->execute();
    $userResult = $userStmt->get_result();
    $userInfo = $userResult->fetch_assoc();
    $currentVip = $userInfo['vip'];
    
    echo "续费前VIP到期: {$currentVip}\n";
    
    // 计算新的VIP到期日期（续费逻辑）
    $currentDate = date('Y-m-d');
    $startDate = ($currentVip > $currentDate) ? $currentVip : $currentDate;
    
    echo "计算起始日期: {$startDate} (当前日期: {$currentDate})\n";
    
    // 根据套餐类型计算会员到期时间
    switch($package) {
        case '1': // 月套餐
            $expireDate = date('Y-m-d', strtotime($startDate . ' +1 month'));
            break;
        case '3': // 季套餐
            $expireDate = date('Y-m-d', strtotime($startDate . ' +3 months'));
            break;
        case 'year': // 年套餐
            $expireDate = date('Y-m-d', strtotime($startDate . ' +1 year'));
            break;
        case 'forever': // 永久套餐
            $expireDate = '2099-12-31';
            break;
        default:
            $expireDate = date('Y-m-d', strtotime($startDate . ' +3 months')); // 默认季套餐
    }
    
    echo "计算的新到期日期: {$expireDate}\n";
    
    // 更新用户会员状态
    $updateUserStmt = $conn->prepare("UPDATE user SET vip = ? WHERE user = ?");
    $updateUserStmt->bind_param("ss", $expireDate, $user);
    $updateUserStmt->execute();
    
    echo "续费完成！新VIP到期: {$expireDate}\n";
    
    return $expireDate;
}

// 测试不同套餐的续费
testRenewal($conn, $test_user, '1', '月套餐续费');
testRenewal($conn, $test_user, '3', '季套餐续费');
testRenewal($conn, $test_user, 'year', '年套餐续费');

echo "\n=== 测试完成 ===\n";

// 显示最终状态
$finalStmt = $conn->prepare("SELECT vip FROM user WHERE user = ?");
$finalStmt->bind_param("s", $test_user);
$finalStmt->execute();
$finalResult = $finalStmt->get_result();
$finalInfo = $finalResult->fetch_assoc();

echo "最终VIP到期时间: {$finalInfo['vip']}\n";

$conn->close();
?>
